import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { logout, selectAuthUser, selectAuthToken } from '../redux/slices/authSlice';
import {
  setInitialized,
  selectDBInitialized,
  initializeDatabase
} from '../redux/slices/chatDBSlice';
import { connectRequest, disconnectRequest, selectConnected } from '../redux/slices/socketSlice';
import { useWatermelonObservable } from '../hooks/useWatermelonObservable';
import { chatDBService } from '../database/service';
import RoomsList from './RoomsList';
import NewChatModal from './NewChatModal';
import { MainScreenNavigationProp } from '../navigation/types';
import { shadows, radius, spacing, typography, useTheme } from '../theme';

interface MainScreenProps {
  navigation: MainScreenNavigationProp;
}

const MainScreen: React.FC<MainScreenProps> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectAuthUser);
  const authToken = useAppSelector(selectAuthToken);
  const dbInitialized = useAppSelector(selectDBInitialized);
  const connected = useAppSelector(selectConnected);
  const { colors } = useTheme();
  const [selectedUsername, setSelectedUsername] = useState<string | null>(null);
  const [showNewChatModal, setShowNewChatModal] = useState<boolean>(false);

  // Use WatermelonDB observables to get rooms
  const rooms = useWatermelonObservable(
    user && dbInitialized ? chatDBService.observeRooms(user) : null,
    []
  );

  // Initialize database on component mount
  useEffect(() => {
    const initDB = async () => {
      try {
        const success = await initializeDatabase();
        dispatch(setInitialized(success));
        if (success) {
          console.log('Database initialized successfully');
        } else {
          console.error('Failed to initialize database');
        }
      } catch (error) {
        console.error('Database initialization error:', error);
        dispatch(setInitialized(false));
      }
    };

    if (!dbInitialized) {
      initDB();
    }
  }, [dispatch, dbInitialized]);

  // Manage socket connection at the app level
  useEffect(() => {
    // Connect to socket when authenticated and database is initialized
    if (user && authToken && dbInitialized) {
      console.log('Connecting to socket from MainScreen component');
      dispatch(connectRequest({ authToken }));
    }

    // Disconnect when the component unmounts or user logs out
    return () => {
      if (connected) {
        console.log('Disconnecting socket from MainScreen component');
        dispatch(disconnectRequest());
      }
    };
  }, [user, authToken, dbInitialized, dispatch]);

  // Auto-reconnect mechanism
  useEffect(() => {
    let reconnectTimer: NodeJS.Timeout | null = null;

    // If authenticated but not connected, try to reconnect
    if (user && authToken && dbInitialized && !connected) {
      console.log('Socket disconnected, attempting to reconnect...');
      reconnectTimer = setTimeout(() => {
        console.log('Reconnecting to socket...');
        dispatch(connectRequest({ authToken }));
      }, 3000); // Try to reconnect after 3 seconds
    }

    // Clean up timer on unmount
    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
    };
  }, [user, authToken, dbInitialized, connected, dispatch]);

  const handleProfilePress = () => {
    // Navigate to profile screen with current user
    if (user) {
      navigation.navigate('Profile', { username: user });
    }
  };

  const handleRoomSelect = (username: string) => {
    setSelectedUsername(username);
    // Navigate to chat screen with the selected user
    navigation.navigate('ChatRoom', { username });
  };

  const handleNewChat = () => {
    setShowNewChatModal(true);
  };

  const handleCloseModal = () => {
    setShowNewChatModal(false);
  };

  const handleStartChat = (username: string) => {
    // Use the existing room select logic to start a new chat
    handleRoomSelect(username);
    setShowNewChatModal(false);
  };

  const getAvatarText = () => {
    return user?.charAt(0).toUpperCase() || '?';
  };

  const styles = createStyles(colors);

  if (!dbInitialized) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Initializing database...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.appName}>Gupt Messenger</Text>
          <TouchableOpacity style={styles.profileButton} onPress={handleProfilePress}>
            <View style={styles.userAvatar}>
              <Text style={styles.avatarText}>{getAvatarText()}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.roomsContainer}>
        <RoomsList
          rooms={rooms}
          onRoomSelect={handleRoomSelect}
          selectedUsername={selectedUsername}
          onNewChat={handleNewChat}
        />
      </View>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.floatingActionButton} onPress={handleNewChat}>
        <Icon name="add" size={28} color={colors.white} />
      </TouchableOpacity>

      <NewChatModal
        isVisible={showNewChatModal}
        onClose={handleCloseModal}
        onStartChat={handleStartChat}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  loadingText: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  header: {
    backgroundColor: colors.cardBackground,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl, // Increased top padding for status bar spacing
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    ...shadows.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  appName: {
    ...typography.h1,
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    fontFamily: 'Outfit',
  },
  profileButton: {
    padding: spacing.xs,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: radius.round,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.sm,
  },
  avatarText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'Outfit',
  },
  floatingActionButton: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
    elevation: 8, // For Android shadow
  },
  roomsContainer: {
    flex: 1,
    paddingTop: spacing.sm, // Add some top padding for better spacing
  },
});

export default MainScreen;
